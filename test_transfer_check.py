#!/usr/bin/env python3
"""
Test script to verify the transfer check functionality.
This script tests the Redis helper functions and transfer status checking.
"""

import asyncio
import redis
from helpers import redis_helper
from models import Customer
from config.settings import settings

async def test_transfer_functionality():
    """Test the transfer check functionality"""
    
    # Setup Redis connection
    rclient = redis.Redis(
        host=settings.REDIS_HOST, 
        port=settings.REDIS_PORT, 
        db=settings.REDIS_DB,
        decode_responses=True,
        username=settings.REDIS_USERNAME,
        password=settings.REDIS_PASS
    )
    
    # Test phone number
    test_phone = "+1234567890"
    
    print("🧪 Testing Transfer Check Functionality")
    print("=" * 50)
    
    # Test 1: Create a customer with is_transferred = False
    print("\n1. Testing customer with is_transferred = False")
    customer_data = {
        'id': 'test-customer-123',
        'phone_number': test_phone,
        'firstname': 'John',
        'lastname': 'Doe',
        'is_transferred': False
    }
    
    # Store in Redis
    redis_data = redis_helper.prepare_for_redis(customer_data)
    key_name = f'customer{test_phone}'
    rclient.hset(name=key_name, mapping=redis_data)
    
    # Retrieve and check
    retrieved_data = rclient.hgetall(key_name)
    restored_data = redis_helper.restore_from_redis(retrieved_data)
    
    print(f"   Original is_transferred: {customer_data['is_transferred']}")
    print(f"   Redis stored value: {redis_data.get('is_transferred')}")
    print(f"   Restored is_transferred: {restored_data.get('is_transferred')}")
    print(f"   Type: {type(restored_data.get('is_transferred'))}")
    
    # Test 2: Update customer to is_transferred = True
    print("\n2. Testing customer with is_transferred = True")
    customer_data['is_transferred'] = True
    
    # Store in Redis
    redis_data = redis_helper.prepare_for_redis(customer_data)
    rclient.hset(name=key_name, mapping=redis_data)
    
    # Retrieve and check
    retrieved_data = rclient.hgetall(key_name)
    restored_data = redis_helper.restore_from_redis(retrieved_data)
    
    print(f"   Original is_transferred: {customer_data['is_transferred']}")
    print(f"   Redis stored value: {redis_data.get('is_transferred')}")
    print(f"   Restored is_transferred: {restored_data.get('is_transferred')}")
    print(f"   Type: {type(restored_data.get('is_transferred'))}")
    
    # Test 3: Test with None value
    print("\n3. Testing customer with is_transferred = None")
    customer_data['is_transferred'] = None
    
    # Store in Redis
    redis_data = redis_helper.prepare_for_redis(customer_data)
    rclient.hset(name=key_name, mapping=redis_data)
    
    # Retrieve and check
    retrieved_data = rclient.hgetall(key_name)
    restored_data = redis_helper.restore_from_redis(retrieved_data)
    
    print(f"   Original is_transferred: {customer_data['is_transferred']}")
    print(f"   Redis stored value: {redis_data.get('is_transferred')}")
    print(f"   Restored is_transferred: {restored_data.get('is_transferred')}")
    print(f"   Type: {type(restored_data.get('is_transferred'))}")
    
    # Test 4: Test missing key
    print("\n4. Testing non-existent customer")
    non_existent_key = f'customer+9999999999'
    retrieved_data = rclient.hgetall(non_existent_key)
    print(f"   Retrieved data for non-existent customer: {retrieved_data}")
    
    # Cleanup
    rclient.delete(key_name)
    print(f"\n✅ Cleanup: Deleted test data for {test_phone}")
    
    print("\n🎉 Transfer functionality test completed!")

if __name__ == "__main__":
    asyncio.run(test_transfer_functionality())
